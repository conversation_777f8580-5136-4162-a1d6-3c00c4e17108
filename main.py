#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Management System - نظام إدارة الاختبارات
A modern Python application for managing test specifications
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import pandas as pd

# --- Matplotlib Imports ---
# These are no longer strictly needed as charts are removed, but kept for potential future use.
import matplotlib.pyplot as plt
from matplotlib import font_manager, rcParams
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
# --- End Matplotlib Imports ---

import json
from datetime import datetime
import os
from typing import Dict, List, Optional

# --- Global Configuration ---
DATA_FILE = "test_data.xlsx"


# --- Font Setup ---
def setup_arabic_font():
    """
    Attempts to find a suitable Arabic font from a file or system, and sets it for Matplotlib.
    CustomTkinter widgets will use this font name when created.
    Falls back to Arial if the specified font file is not found or loading fails.
    """
    preferred_fonts = [
        'Cairo', 'Cairo Regular', 'Cairo-Regular',
        'Noto Sans Arabic', 'Arial Unicode MS', 'Tahoma', 'Times New Roman', 'Arial'
    ]

    selected_font_name = "Arial"  # Default fallback font
    font_path_from_file = "fonts/Amiri-Regular.ttf"  # Path to your font file

    try:
        # --- Load Font from File ---
        if os.path.exists(font_path_from_file):
            font_manager.fontManager.addfont(font_path_from_file)
            font_properties = font_manager.FontProperties(fname=font_path_from_file)
            registered_font_name_from_file = font_properties.get_name()

            # Get available system font names for verification
            system_font_names_lower = {f.name.lower() for f in font_manager.fontManager.ttflist}

            # Check if the font loaded from file is now recognized by the system
            if registered_font_name_from_file and registered_font_name_from_file.lower() in system_font_names_lower:
                selected_font_name = registered_font_name_from_file
                print(f"Font successfully loaded and registered: '{selected_font_name}' from {font_path_from_file}")
            else:
                print(
                    f"Warning: Font '{registered_font_name_from_file}' from file not found in system fonts after adding. Falling back.")

        # --- Fallback to System Fonts if File Loading Failed or Font Not Found ---
        if selected_font_name == "Arial":  # If fallback is still needed
            system_font_names_lower = {f.name.lower() for f in font_manager.fontManager.ttflist}
            for pref_font in preferred_fonts:
                if pref_font.lower() in system_font_names_lower:
                    selected_font_name = pref_font
                    print(f"Using system font: {selected_font_name}")
                    break
            if selected_font_name == "Arial" and 'arial' not in system_font_names_lower:
                print("Arial not found in system fonts, using default Matplotlib font.")

        # --- Set Fonts ---
        # CustomTkinter widgets use the font name string passed during creation.
        # Matplotlib uses rcParams for global font settings.
        rcParams['font.family'] = selected_font_name
        rcParams['axes.unicode_minus'] = False

    except Exception as e:
        print(f"Error loading or setting Arabic font: {e}. Falling back to Arial.")
        selected_font_name = "Arial"

    return selected_font_name  # Return the name of the font being used


# --- Dialog for Adding/Editing Tests ---
class TestDialog(ctk.CTkToplevel):
    def __init__(self, parent, test_data: Optional[Dict] = None, arabic_font: str = "Arial",
                 all_tests_data: List[Dict] = None):
        super().__init__(parent)
        self.title("إضافة/تعديل اختبار" if test_data is None else "تعديل اختبار")
        self.geometry("850x700")
        self.transient(parent)
        self.grab_set()

        self.test_data = test_data
        self.arabic_font = arabic_font
        self.all_tests_data = all_tests_data
        self.result = None

        # Center the dialog window relative to its parent
        self.update_idletasks()
        parent_geo = parent.geometry().split('+')
        parent_x, parent_y = int(parent_geo[1]), int(parent_geo[2])
        parent_width, parent_height = int(parent_geo[0][:parent_geo[0].find('x')]), int(
            parent_geo[0][parent_geo[0].find('x') + 1:])

        dialog_width = 850
        dialog_height = 700
        x = parent_x + (parent_width // 2) - (dialog_width // 2)
        y = parent_y + (parent_height // 2) - (dialog_height // 2)
        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        self.setup_dialog_ui()
        if self.test_data:
            self.load_data_into_form()

    def setup_dialog_ui(self):
        self.main_frame = ctk.CTkScrollableFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        title_text = "تعديل اختبار" if self.test_data else "إضافة اختبار جديد"
        title_label = ctk.CTkLabel(self.main_frame, text=title_text,
                                   font=ctk.CTkFont(family=self.arabic_font, size=22, weight="bold"))
        title_label.pack(pady=(10, 25))

        self.entry_widgets = {}

        self.fields_config = [
            {"key": "test_name", "label": "اسم الاختبار:", "required": True, "widget": "entry"},
            {"key": "test_code", "label": "رمز الاختبار:", "required": True, "widget": "entry"},
            {"key": "published_models", "label": "عدد النماذج المنشورة:", "widget": "entry", "type": "number"},
            {"key": "publish_date_1", "label": "تاريخ النشر (1):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "stop_date_1", "label": "تاريخ الإيقاف (1):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "publish_date_2", "label": "تاريخ النشر (2):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "stop_date_2", "label": "تاريخ الإيقاف (2):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "publish_date_3", "label": "تاريخ النشر (3):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "stop_date_3", "label": "تاريخ الإيقاف (3):", "widget": "entry", "placeholder": "DD/MM/YYYY"},
            {"key": "models_or_sections", "label": "نماذج أو أقسام:", "widget": "combobox",
             "values": ["نماذج", "أقسام"]},
            {"key": "active_tests", "label": "عدد الاختبارات النشطة:", "widget": "entry", "type": "number"},
            {"key": "inactive_tests", "label": "عدد الاختبارات الغير نشطة:", "widget": "entry", "type": "number"},
            {"key": "notes", "label": "ملاحظات:", "widget": "textbox"}
        ]

        for config in self.fields_config:
            if config["widget"] == "entry":
                self.create_entry_field(self.main_frame, config)
            elif config["widget"] == "combobox":
                self.create_combobox_field(self.main_frame, config)
            elif config["widget"] == "textbox":
                self.create_textbox_field(self.main_frame, config)

        button_frame = ctk.CTkFrame(self.main_frame)
        button_frame.pack(fill="x", pady=(30, 10))

        save_btn = ctk.CTkButton(
            button_frame,
            text="حفظ",
            command=self.save_test_data,
            font=ctk.CTkFont(family=self.arabic_font, size=14, weight="bold"),
            height=40
        )
        save_btn.pack(side="right", padx=(10, 0))

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="إلغاء",
            command=self.dialog_close,
            fg_color="gray",
            hover_color="darkgray",
            font=ctk.CTkFont(family=self.arabic_font, size=14),
            height=40
        )
        cancel_btn.pack(side="right")

    def create_entry_field(self, parent, config):
        row_frame = ctk.CTkFrame(parent)
        row_frame.pack(fill="x", pady=5)
        label_text = config["label"] + " *" if config.get("required") else config["label"]
        label = ctk.CTkLabel(row_frame, text=label_text,
                             font=ctk.CTkFont(family=self.arabic_font, size=12, weight="bold"))
        label.pack(side="right", padx=(0, 10), pady=5)

        entry_kwargs = {"placeholder_text": config.get("placeholder", ""),
                        "font": ctk.CTkFont(family=self.arabic_font, size=12), "height": 35}
        entry = ctk.CTkEntry(row_frame, **entry_kwargs)
        entry.pack(side="right", fill="x", expand=True, padx=(0, 10), pady=5)
        self.entry_widgets[config["key"]] = entry

    def create_combobox_field(self, parent, config):
        row_frame = ctk.CTkFrame(parent)
        row_frame.pack(fill="x", pady=5)
        label_text = config["label"]
        label = ctk.CTkLabel(row_frame, text=label_text,
                             font=ctk.CTkFont(family=self.arabic_font, size=12, weight="bold"))
        label.pack(side="right", padx=(0, 10), pady=5)

        combobox = ctk.CTkComboBox(
            row_frame,
            values=config["values"],
            variable=tk.StringVar(),
            font=ctk.CTkFont(family=self.arabic_font, size=12),
            height=35,
            dropdown_font=ctk.CTkFont(family=self.arabic_font, size=12)
        )
        combobox.pack(side="right", fill="x", expand=True, padx=(0, 10), pady=5)
        self.entry_widgets[config["key"]] = combobox

    def create_textbox_field(self, parent, config):
        row_frame = ctk.CTkFrame(parent)
        row_frame.pack(fill="x", pady=5)
        label_text = config["label"]
        label = ctk.CTkLabel(row_frame, text=label_text,
                             font=ctk.CTkFont(family=self.arabic_font, size=12, weight="bold"))
        label.pack(anchor="ne", padx=(0, 10), pady=(10, 5))

        textbox = ctk.CTkTextbox(row_frame, height=80, font=ctk.CTkFont(family=self.arabic_font, size=12))
        textbox.pack(fill="x", expand=True, padx=10, pady=(0, 10))
        self.entry_widgets[config["key"]] = textbox

    def validate_date(self, date_str: str, key: str, label: str) -> Optional[str]:
        if not date_str: return ""
        try:
            datetime.strptime(date_str, "%d/%m/%Y")
            return date_str
        except ValueError:
            try:
                datetime.strptime(date_str, "%m/%d/%Y")
                return date_str
            except ValueError:
                messagebox.showerror("خطأ في التنسيق",
                                     f"تنسيق التاريخ غير صحيح للحقل '{label}'. يرجى استخدام DD/MM/YYYY.")
                self.entry_widgets[key].focus()
                return None

    def validate_number(self, number_str: str, key: str, label: str) -> Optional[int]:
        if not number_str: return 0
        try:
            return int(number_str)
        except ValueError:
            messagebox.showerror("خطأ في القيمة", f"يجب أن يكون الحقل '{label}' رقماً صحيحاً.")
            self.entry_widgets[key].focus()
            return None

    def collect_and_validate_data(self) -> Optional[Dict]:
        collected_data = {}
        for config in self.fields_config:
            key = config["key"]
            widget = self.entry_widgets[key]
            raw_value = ""

            if isinstance(widget, ctk.CTkTextbox):
                raw_value = widget.get("1.0", "end-1c").strip()
            elif isinstance(widget, ctk.CTkComboBox):
                raw_value = widget.get().strip()
            else:  # CTkEntry
                raw_value = widget.get().strip()

            if config.get("required") and not raw_value:
                messagebox.showerror("خطأ", f"الحقل '{config['label']}' مطلوب.")
                widget.focus()
                return None

            validated_value = raw_value

            if key in ["publish_date_1", "stop_date_1", "publish_date_2", "stop_date_2", "publish_date_3",
                       "stop_date_3"]:
                validated_value = self.validate_date(raw_value, key, config["label"])
                if validated_value is None: return None

            elif config.get("type") == "number":
                validated_value = self.validate_number(raw_value, key, config["label"])
                if validated_value is None: return None

            collected_data[key] = validated_value

        if not self.test_data:  # Check duplicates only when ADDING
            new_test_code = collected_data.get("test_code")
            if new_test_code and self.all_tests_data:
                for existing_test in self.all_tests_data:
                    if existing_test.get("test_code") == new_test_code:
                        messagebox.showerror("خطأ", f"رمز الاختبار '{new_test_code}' موجود بالفعل.")
                        self.entry_widgets["test_code"].focus()
                        return None

        return collected_data

    def save_test_data(self):
        validated_data = self.collect_and_validate_data()
        if validated_data:
            self.result = validated_data
            self.dialog_close()

    def dialog_close(self):
        self.destroy()

    def load_data_into_form(self):
        if not self.test_data: return
        for key, widget in self.entry_widgets.items():
            value = self.test_data.get(key, "")
            if isinstance(widget, ctk.CTkTextbox):
                widget.delete("1.0", "end")
                widget.insert("1.0", str(value))
            elif isinstance(widget, ctk.CTkComboBox):
                widget.set(str(value))
            else:  # CTkEntry
                widget.delete(0, "end")
                widget.insert(0, str(value))


# --- Dialog for Queries ---
class QueryDialog(ctk.CTkToplevel):
    def __init__(self, parent, arabic_font: str = "Arial", all_tests_data: List[Dict] = None):
        super().__init__(parent)
        self.title("استعلام عن البيانات")
        self.geometry("500x550")
        self.transient(parent)
        self.grab_set()

        self.arabic_font = arabic_font
        self.all_tests_data = all_tests_data
        self.selected_test_data = None

        # Center the dialog
        self.update_idletasks()
        parent_geo = parent.geometry().split('+')
        parent_x, parent_y = int(parent_geo[1]), int(parent_geo[2])
        parent_width, parent_height = int(parent_geo[0][:parent_geo[0].find('x')]), int(
            parent_geo[0][parent_geo[0].find('x') + 1:])

        dialog_width = 500
        dialog_height = 550
        x = parent_x + (parent_width // 2) - (dialog_width // 2)
        y = parent_y + (parent_height // 2) - (dialog_height // 2)
        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        self.setup_ui()

    def setup_ui(self):
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        title_label = ctk.CTkLabel(main_frame, text="اختر اختباراً ثم البيانات المطلوبة",
                                   font=ctk.CTkFont(family=self.arabic_font, size=18, weight="bold"))
        title_label.pack(pady=(0, 20))

        # --- Step 1: Select Test Name ---
        self.test_name_frame = ctk.CTkFrame(main_frame)
        self.test_name_frame.pack(fill="x", pady=10)

        test_name_label = ctk.CTkLabel(self.test_name_frame, text="اسم الاختبار:",
                                       font=ctk.CTkFont(family=self.arabic_font, size=12, weight="bold"))
        test_name_label.pack(side="right", padx=(0, 10))

        self.all_test_names = sorted(list(set(test.get("test_name", "N/A") for test in self.all_tests_data)))
        self.test_name_var = tk.StringVar(value="اختر اختباراً")
        self.test_name_dropdown = ctk.CTkComboBox(
            self.test_name_frame,
            values=self.all_test_names,
            variable=self.test_name_var,
            command=self.on_test_name_selected,
            font=ctk.CTkFont(family=self.arabic_font, size=12),
            height=35,
            dropdown_font=ctk.CTkFont(family=self.arabic_font, size=12)
        )
        self.test_name_dropdown.pack(side="right", fill="x", expand=True, padx=(0, 10))

        # --- Step 2: Select Data Field ---
        self.data_field_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        self.data_field_frame.pack(fill="x", pady=10)

        data_field_label = ctk.CTkLabel(self.data_field_frame, text="البيانات المطلوبة:",
                                        font=ctk.CTkFont(family=self.arabic_font, size=12, weight="bold"))
        data_field_label.pack(side="right", padx=(0, 10))

        self.available_data_fields = [
            "عدد النماذج المنشورة", "تاريخ النشر (1)", "تاريخ الإيقاف (1)",
            "تاريخ النشر (2)", "تاريخ الإيقاف (2)", "تاريخ النشر (3)", "تاريخ الإيقاف (3)",
            "نماذج أو أقسام", "عدد الاختبارات النشطة", "عدد الاختبارات الغير نشطة", "ملاحظات"
        ]
        self.data_field_var = tk.StringVar(value="اختر البيانات")
        self.data_field_dropdown = ctk.CTkComboBox(
            self.data_field_frame,
            values=self.available_data_fields,
            variable=self.data_field_var,
            command=self.on_data_field_selected,
            font=ctk.CTkFont(family=self.arabic_font, size=12),
            height=35,
            dropdown_font=ctk.CTkFont(family=self.arabic_font, size=12),
            state="disabled"
        )
        self.data_field_dropdown.pack(side="right", fill="x", expand=True, padx=(0, 10))

        # --- Display Area for Selected Test Data ---
        self.result_display_frame = ctk.CTkFrame(main_frame, corner_radius=10, border_width=1, border_color="#CCCCCC")
        self.result_display_frame.pack(fill="both", expand=True, pady=(20, 10))
        self.result_display_label = ctk.CTkLabel(self.result_display_frame,
                                                 text="تفاصيل الاختبار والبيانات المطلوبة ستظهر هنا.", justify="center",
                                                 font=ctk.CTkFont(family=self.arabic_font, size=14))
        self.result_display_label.pack(padx=10, pady=10, expand=True)

        # --- Close Button ---
        close_btn = ctk.CTkButton(
            main_frame,
            text="إغلاق",
            command=self.destroy,
            fg_color="gray",
            hover_color="darkgray",
            font=ctk.CTkFont(family=self.arabic_font, size=14),
            height=40
        )
        close_btn.pack(pady=(10, 0))

    def on_test_name_selected(self, selected_test_name):
        """Called when a test name is selected from the first dropdown."""
        if selected_test_name == "اختر اختباراً":
            self.data_field_dropdown.configure(values=[""], state="disabled",
                                               variable=tk.StringVar(value="اختر البيانات"))
            self.result_display_label.configure(text="تفاصيل الاختبار والبيانات المطلوبة ستظهر هنا.")
            self.selected_test_data = None
            return

        self.data_field_dropdown.configure(state="normal", variable=tk.StringVar(value="اختر البيانات"))
        self.result_display_label.configure(text="تفاصيل الاختبار المحدد ستظهر هنا.")
        self.selected_test_data = None

    def on_data_field_selected(self, selected_data_field_string):
        """Called when a data field is selected from the second dropdown."""
        if not selected_data_field_string or selected_data_field_string == "اختر البيانات":
            self.result_display_label.configure(text="تفاصيل الاختبار والبيانات المطلوبة ستظهر هنا.")
            self.selected_test_data = None
            return

        selected_test_name = self.test_name_var.get()

        test_data_for_name = next((test for test in self.all_tests_data if test.get("test_name") == selected_test_name),
                                  None)

        if test_data_for_name:
            self.selected_test_data = test_data_for_name
            field_value = test_data_for_name.get(self.get_internal_field_key(selected_data_field_string), "N/A")
            self.display_test_details({selected_data_field_string: field_value})
        else:
            self.result_display_label.configure(text="لم يتم العثور على بيانات الاختبار.")
            self.selected_test_data = None

    def get_internal_field_key(self, display_name):
        """Maps the Arabic display name back to the internal English key for data lookup."""
        key_map = {
            "عدد النماذج المنشورة": "published_models", "تاريخ النشر (1)": "publish_date_1",
            "تاريخ الإيقاف (1)": "stop_date_1",
            "تاريخ النشر (2)": "publish_date_2", "تاريخ الإيقاف (2)": "stop_date_2",
            "تاريخ النشر (3)": "publish_date_3", "تاريخ الإيقاف (3)": "stop_date_3",
            "نماذج أو أقسام": "models_or_sections", "عدد الاختبارات النشطة": "active_tests",
            "عدد الاختبارات الغير نشطة": "inactive_tests", "ملاحظات": "notes"
        }
        return key_map.get(display_name, display_name)

    def display_test_details(self, data_dict):
        """Formats and displays specific data field values."""
        if not data_dict:
            self.result_display_label.configure(text="تفاصيل الاختبار المحدد ستظهر هنا.")
            return

        details = "البيانات المطلوبة:\n\n"
        for field_name, value in data_dict.items():
            details += f"  {field_name}: {value}\n"

        self.result_display_label.configure(text=details)


# --- Main Application Class ---
class TestManagementApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("نظام إدارة الاختبارات - Test Management System")
        self.root.geometry("1400x800")
        self.root.minsize(1200, 700)

        self.arabic_font = setup_arabic_font()  # Setup the Arabic font

        self.tests_data: List[Dict] = []
        self.load_data_from_file()

        self.setup_main_ui()

    def load_data_from_file(self):
        """Loads test data from the specified Excel file."""
        if not os.path.exists(DATA_FILE):
            print(f"ملف البيانات '{DATA_FILE}' غير موجود. سيتم إنشاء ملف جديد عند الحفظ.")
            self.tests_data = []
            return

        try:
            df = pd.read_excel(DATA_FILE)
            self.tests_data = df.to_dict('records')

            arabic_to_english_map = {
                "اسم الاختبار": "test_name", "رمز الاختبار": "test_code", "عدد النماذج المنشورة": "published_models",
                "تاريخ النشر (1)": "publish_date_1", "تاريخ الإيقاف (1)": "stop_date_1",
                "تاريخ النشر (2)": "publish_date_2", "تاريخ الإيقاف (2)": "stop_date_2",
                "تاريخ النشر (3)": "publish_date_3", "تاريخ الإيقاف (3)": "stop_date_3",
                "نماذج أو أقسام": "models_or_sections", "عدد الاختبارات النشطة": "active_tests",
                "عدد الاختبارات الغير نشطة": "inactive_tests", "ملاحظات": "notes"
            }

            if self.tests_data:
                first_item_keys = self.tests_data[0].keys()
                if any(ar_key in first_item_keys for ar_key in arabic_to_english_map):
                    new_data = []
                    for item in self.tests_data:
                        converted_item = {}
                        for ar_key, en_key in arabic_to_english_map.items():
                            converted_item[en_key] = item.get(ar_key, "")
                        new_data.append(converted_item)
                    self.tests_data = new_data

            print(f"تم تحميل {len(self.tests_data)} سجل اختبار من '{DATA_FILE}'.")

        except FileNotFoundError:
            print(f"ملف البيانات '{DATA_FILE}' لم يتم العثور عليه.")
            self.tests_data = []
        except Exception as e:
            print(f"حدث خطأ أثناء تحميل البيانات من '{DATA_FILE}': {e}")
            messagebox.showerror("خطأ في التحميل", f"تعذر تحميل البيانات: {e}\nسيتم العمل ببيانات فارغة.")
            self.tests_data = []

    def save_data_to_file(self):
        """Saves the current test data to the Excel file."""
        if not self.tests_data:
            print("لا توجد بيانات لحفظها.")
            return False

        try:
            english_to_arabic_map = {
                "test_name": "اسم الاختبار", "test_code": "رمز الاختبار", "published_models": "عدد النماذج المنشورة",
                "publish_date_1": "تاريخ النشر (1)", "stop_date_1": "تاريخ الإيقاف (1)",
                "publish_date_2": "تاريخ النشر (2)", "stop_date_2": "تاريخ الإيقاف (2)",
                "publish_date_3": "تاريخ النشر (3)", "stop_date_3": "تاريخ الإيقاف (3)",
                "models_or_sections": "نماذج أو أقسام", "active_tests": "عدد الاختبارات النشطة",
                "inactive_tests": "عدد الاختبارات الغير نشطة", "notes": "ملاحظات"
            }

            df = pd.DataFrame(self.tests_data)
            df = df.rename(columns=english_to_arabic_map)

            df.to_excel(DATA_FILE, index=False)
            print(f"تم حفظ البيانات بنجاح إلى '{DATA_FILE}'.")
            return True
        except Exception as e:
            print(f"فشل حفظ البيانات إلى '{DATA_FILE}': {e}")
            messagebox.showerror("خطأ في الحفظ", f"تعذر حفظ البيانات: {e}")
            return False

    def setup_main_ui(self):
        """Configures the main application window and its widgets."""
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="نظام إدارة الاختبارات المنشورة",
            font=ctk.CTkFont(family=self.arabic_font, size=26, weight="bold"),
            text_color="#0078D4"
        )
        title_label.pack(pady=(20, 30))

        controls_frame = ctk.CTkFrame(self.main_frame)
        controls_frame.pack(fill="x", padx=10, pady=(0, 20))

        buttons_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        buttons_frame.pack(side="right", fill="y", padx=10)

        self.add_btn = ctk.CTkButton(
            buttons_frame, text="إضافة اختبار جديد", command=self.open_add_dialog,
            font=ctk.CTkFont(family=self.arabic_font, size=14, weight="bold"), height=40
        )
        self.add_btn.pack(side="right", padx=(5, 0))

        self.edit_btn = ctk.CTkButton(
            buttons_frame, text="تعديل الاختبار المحدد", command=self.open_edit_dialog,
            font=ctk.CTkFont(family=self.arabic_font, size=14), height=40,
            state="disabled", fg_color="#808080"
        )
        self.edit_btn.pack(side="right", padx=5)

        self.delete_btn = ctk.CTkButton(
            buttons_frame, text="حذف الاختبار المحدد", command=self.delete_selected_test,
            fg_color="#D13434", hover_color="#B32D35",
            font=ctk.CTkFont(family=self.arabic_font, size=14), height=40,
            state="disabled"
        )
        self.delete_btn.pack(side="right", padx=5)

        io_buttons_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        io_buttons_frame.pack(side="left", fill="y", padx=10)

        self.import_btn = ctk.CTkButton(
            io_buttons_frame, text="استيراد البيانات", command=self.import_data,
            font=ctk.CTkFont(family=self.arabic_font, size=13), height=35, width=150
        )
        self.import_btn.pack(side="left", padx=(0, 5))

        self.export_btn = ctk.CTkButton(
            io_buttons_frame, text="تصدير البيانات", command=self.export_data,
            font=ctk.CTkFont(family=self.arabic_font, size=13), height=35, width=150
        )
        self.export_btn.pack(side="left", padx=5)

        search_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        search_frame.pack(side="left", fill="x", expand=True, padx=10)

        search_label = ctk.CTkLabel(search_frame, text="البحث:",
                                    font=ctk.CTkFont(family=self.arabic_font, size=14, weight="bold"))
        search_label.pack(side="right", padx=(0, 10))

        self.search_var = tk.StringVar()
        self.search_var.trace_add("write", self.filter_tests)
        self.search_entry = ctk.CTkEntry(
            search_frame,
            textvariable=self.search_var,
            placeholder_text="ابحث عن اسم الاختبار، رمزه، ملاحظاته، أو عدد النماذج...",
            font=ctk.CTkFont(family=self.arabic_font, size=12),
            height=35
        )
        self.search_entry.pack(side="right", fill="x", expand=True, padx=(0, 10))

        self.show_charts_btn = ctk.CTkButton(
            controls_frame, text="إظهار الرسوم البيانية",
            command=self.toggle_charts_visibility,
            font=ctk.CTkFont(family=self.arabic_font, size=13),
            height=35, width=170
        )
        self.show_charts_btn.pack(side="left", padx=(10, 5))

        self.query_btn = ctk.CTkButton(
            controls_frame,
            text="استعلام عن اختبار",
            command=self.open_query_dialog,
            font=ctk.CTkFont(family=self.arabic_font, size=13),
            height=35,
            width=170
        )
        self.query_btn.pack(side="left", padx=(5, 10))

        # --- Table Frame ---
        table_outer_frame = ctk.CTkFrame(self.main_frame, corner_radius=10, border_width=1, border_color="#CCCCCC")
        table_outer_frame.pack(fill="both", expand=True, padx=10, pady=(0, 20))
        self.setup_treeview_table(table_outer_frame)  # Setup the table widget

        # --- Chart Frame ---
        # Removed chart frame as per the request.
        # self.charts_frame_container = ctk.CTkFrame(self.main_frame, height=300, corner_radius=10, border_width=1, border_color="#CCCCCC")
        # self.charts_frame_container.pack(fill="x", padx=10, pady=(0, 20))
        # self.charts_frame_container.pack_forget()

        # Initially populate the table with data
        self.refresh_table_display()

    def toggle_charts_visibility(self):
        """This method is now obsolete as charts are removed."""
        pass

    def render_charts(self):
        """This method is now obsolete as charts are removed."""
        pass

    def refresh_table_display(self, data_to_display: Optional[List[Dict]] = None):
        """Clears the Treeview and repopulates it with the provided or current data."""
        # Clear all existing rows from the Treeview
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Determine which data source to use for populating the table
        data_source = data_to_display if data_to_display is not None else self.tests_data

        # Insert each test record into the Treeview
        for test in data_source:
            # Create a list of values for the row, ensuring all are strings and handle missing keys
            values = []
            for col in self.columns:
                if col == "publish_dates":
                    # Combine all publish dates into one column with better formatting
                    dates = []
                    for i in range(1, 4):
                        date = test.get(f"publish_date_{i}", "")
                        if date:
                            dates.append(f" {i}: {date}")
                    values.append("\n".join(dates) if dates else "لا توجد تواريخ")
                elif col == "stop_dates":
                    # Combine all stop dates into one column with better formatting
                    dates = []
                    for i in range(1, 4):
                        date = test.get(f"stop_date_{i}", "")
                        if date:
                            dates.append(f"الإيقاف {i}: {date}")
                    values.append("\n".join(dates) if dates else "لا توجد تواريخ")
                else:
                    values.append(str(test.get(col, "")))

            # Insert row into the Treeview
            self.tree.insert("", "end", values=values)

        # Update button states (to disable them if no row is selected after refresh)
        self.on_tree_selection()

        # Manage chart visibility based on whether there's any data
        # Since charts are removed, this block is no longer needed for managing their visibility.
        # if self.tests_data:
        #     pass
        # else:
        #     self.charts_frame_container.pack_forget()
        #     self.show_charts_btn.configure(text="إظهار الرسوم البيانية")

    def filter_tests(self, *args):
        """Filters the table data based on the text in the search entry."""
        search_term = self.search_var.get().lower().strip()

        if not search_term:
            self.refresh_table_display()
            return

        filtered_data = []
        for test in self.tests_data:
            test_name = str(test.get("test_name", "")).lower()
            test_code = str(test.get("test_code", "")).lower()
            notes = str(test.get("notes", "")).lower()
            published_models = str(test.get("published_models", "")).lower()
            active_tests = str(test.get("active_tests", "")).lower()
            inactive_tests = str(test.get("inactive_tests", "")).lower()
            models_or_sections = str(test.get("models_or_sections", "")).lower()

            if (search_term in test_name or
                    search_term in test_code or
                    search_term in notes or
                    search_term in published_models or
                    search_term in active_tests or
                    search_term in inactive_tests or
                    search_term in models_or_sections):
                filtered_data.append(test)

        self.refresh_table_display(filtered_data)

    def open_add_dialog(self):
        """Opens the dialog window for adding a new test."""
        dialog = TestDialog(self.root, arabic_font=self.arabic_font, all_tests_data=self.tests_data)
        self.root.wait_window(dialog)

        if dialog.result:
            self.tests_data.append(dialog.result)
            self.refresh_table_display()
            self.save_data_to_file()

    def get_selected_test_data(self) -> Optional[Dict]:
        """Retrieves the data of the currently selected test in the Treeview."""
        selected_item_id = self.tree.selection()
        if not selected_item_id:
            messagebox.showwarning("تحذير", "يرجى تحديد اختبار من الجدول.")
            return None

        item_values = self.tree.item(selected_item_id[0])["values"]

        try:
            test_code_index = self.columns.index("test_code")
            selected_test_code = item_values[test_code_index]
        except (ValueError, IndexError):
            messagebox.showerror("خطأ", "لا يمكن تحديد رمز الاختبار من الجدول.")
            return None

        for test in self.tests_data:
            if test.get("test_code") == selected_test_code:
                return test

        messagebox.showerror("خطأ", f"لم يتم العثور على بيانات الاختبار برمز '{selected_test_code}' في مصدر البيانات.")
        return None

    def open_edit_dialog(self):
        """Opens the dialog window for editing the currently selected test."""
        selected_test = self.get_selected_test_data()
        if not selected_test:
            return

        dialog = TestDialog(self.root, test_data=selected_test.copy(), arabic_font=self.arabic_font,
                            all_tests_data=self.tests_data)
        self.root.wait_window(dialog)

        if dialog.result:
            try:
                test_code_to_update = selected_test.get("test_code")
                if test_code_to_update:
                    for i, test in enumerate(self.tests_data):
                        if test.get("test_code") == test_code_to_update:
                            self.tests_data[i] = dialog.result
                            self.refresh_table_display()
                            self.save_data_to_file()
                            return
                else:
                    messagebox.showerror("خطأ", "لا يمكن تعديل الاختبار بدون رمز اختبار صالح.")
            except Exception as e:
                messagebox.showerror("خطأ في التحديث", f"حدث خطأ أثناء تحديث البيانات: {e}")

    def delete_selected_test(self):
        """Deletes the currently selected test from the data and updates the UI."""
        selected_test = self.get_selected_test_data()
        if not selected_test:
            return

        test_code = selected_test.get("test_code")
        test_name = selected_test.get("test_name", "الاختبار")

        if messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد أنك تريد حذف الاختبار '{test_name}' (رمز: {test_code})؟",
                icon='warning'
        ):
            try:
                self.tests_data = [test for test in self.tests_data if test.get("test_code") != test_code]

                self.refresh_table_display()
                self.save_data_to_file()
                messagebox.showinfo("نجاح", f"تم حذف الاختبار '{test_name}' بنجاح.")
            except Exception as e:
                messagebox.showerror("خطأ في الحذف", f"حدث خطأ أثناء حذف الاختبار: {e}")

    def import_data(self):
        """Handles importing data from an Excel file selected by the user."""
        try:
            filepath = filedialog.askopenfilename(
                title="استيراد البيانات من ملف Excel",
                filetypes=[("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")],
                defaultextension=".xlsx"
            )
            if not filepath: return

            df = pd.read_excel(filepath)
            imported_data = df.to_dict('records')

            arabic_to_english_map = {
                "اسم الاختبار": "test_name", "رمز الاختبار": "test_code", "عدد النماذج المنشورة": "published_models",
                "تاريخ النشر (1)": "publish_date_1", "تاريخ الإيقاف (1)": "stop_date_1",
                "تاريخ النشر (2)": "publish_date_2", "تاريخ الإيقاف (2)": "stop_date_2",
                "تاريخ النشر (3)": "publish_date_3", "تاريخ الإيقاف (3)": "stop_date_3",
                "نماذج أو أقسام": "models_or_sections", "عدد الاختبارات النشطة": "active_tests",
                "عدد الاختبارات الغير نشطة": "inactive_tests", "ملاحظات": "notes"
            }

            processed_data = []
            for item in imported_data:
                converted_item = {}
                for ar_key, en_key in arabic_to_english_map.items():
                    converted_item[en_key] = item.get(ar_key, "")
                processed_data.append(converted_item)

            self.tests_data.extend(processed_data)
            self.refresh_table_display()
            self.save_data_to_file()
            messagebox.showinfo("استيراد ناجح", f"تم استيراد {len(processed_data)} سجل اختبار بنجاح.")

        except FileNotFoundError:
            messagebox.showerror("خطأ", "الملف المحدد غير موجود.")
        except Exception as e:
            messagebox.showerror("خطأ في الاستيراد", f"تعذر استيراد البيانات: {e}")

    def export_data(self):
        """Handles exporting the current data to an Excel file."""
        try:
            filepath = filedialog.asksaveasfilename(
                title="تصدير البيانات إلى ملف Excel",
                filetypes=[("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")],
                defaultextension=".xlsx",
                initialfile="exported_tests_data.xlsx"
            )
            if not filepath: return

            df = pd.DataFrame(self.tests_data)

            english_to_arabic_map = {
                "test_name": "اسم الاختبار", "test_code": "رمز الاختبار", "published_models": "عدد النماذج المنشورة",
                "publish_date_1": "تاريخ النشر (1)", "stop_date_1": "تاريخ الإيقاف (1)",
                "publish_date_2": "تاريخ النشر (2)", "stop_date_2": "تاريخ الإيقاف (2)",
                "publish_date_3": "تاريخ النشر (3)", "stop_date_3": "تاريخ الإيقاف (3)",
                "models_or_sections": "نماذج أو أقسام", "active_tests": "عدد الاختبارات النشطة",
                "inactive_tests": "عدد الاختبارات الغير نشطة", "notes": "ملاحظات"
            }
            df = df.rename(columns=english_to_arabic_map)

            df.to_excel(filepath, index=False)
            messagebox.showinfo("تصدير ناجح", f"تم تصدير البيانات إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"تعذر تصدير البيانات: {e}")

    # --- Query Functionality ---
    def open_query_dialog(self):
        """Opens the dialog for performing multi-step queries."""
        # Instantiate the QueryDialog, passing necessary data and configuration
        # Use the arabic_font obtained during initialization
        query_dialog = QueryDialog(self.root, arabic_font=self.arabic_font, all_tests_data=self.tests_data)
        self.root.wait_window(query_dialog)  # Wait for the dialog to close

        if query_dialog.selected_test_data:
            # If a specific test was selected in the query dialog, highlight it in the main table.
            self.highlight_test_in_table(query_dialog.selected_test_data.get("test_code"))
            # Optionally, you could display more details here or trigger other actions.
            # For example, showing details in a message box:
            # messagebox.showinfo("نتيجة الاستعلام", self.format_test_details(query_dialog.selected_test_data))

    def highlight_test_in_table(self, test_code_to_highlight):
        """Highlights a specific test in the main table by its test code."""
        if not test_code_to_highlight:
            return

        # Find the item in the treeview that corresponds to the test code
        for item_id in self.tree.get_children():
            item_values = self.tree.item(item_id)["values"]
            # Assuming test_code is at index 1 (0-indexed) in item_values
            try:
                if item_values[1] == test_code_to_highlight:
                    self.tree.selection_set(item_id)  # Select the item
                    self.tree.focus(item_id)  # Set focus to the item
                    self.tree.see(item_id)  # Scroll to make the item visible
                    break  # Stop searching once found
            except IndexError:
                continue  # Skip if item_values doesn't have enough elements

    def format_test_details(self, test_data):
        """Helper to format test details for display (e.g., in a message box)."""
        if not test_data:
            return "لا توجد بيانات لعرضها."

        details = "تفاصيل الاختبار:\n\n"
        # Format details, ensuring Arabic labels are clear and use the specified font
        details += f"  اسم الاختبار: {test_data.get('test_name', 'N/A')}\n"
        details += f"  رمز الاختبار: {test_data.get('test_code', 'N/A')}\n"
        details += f"  النماذج المنشورة: {test_data.get('published_models', 'N/A')}\n"
        details += f"  تاريخ النشر (1): {test_data.get('publish_date_1', 'N/A')}\n"
        details += f"  تاريخ الإيقاف (1): {test_data.get('stop_date_1', 'N/A')}\n"
        details += f"  النماذج/الأقسام: {test_data.get('models_or_sections', 'N/A')}\n"
        details += f"  الاختبارات النشطة: {test_data.get('active_tests', 'N/A')}\n"
        details += f"  الاختبارات غير النشطة: {test_data.get('inactive_tests', 'N/A')}\n"
        details += f"  ملاحظات: {test_data.get('notes', 'N/A')}\n"
        return details

    def setup_treeview_table(self, parent):
        """Sets up the Treeview widget to display test data."""
        # Define the columns using internal English keys
        self.columns = [
            "test_name", "test_code", "published_models", "publish_dates", "stop_dates", "models_or_sections",
            "active_tests", "inactive_tests", "notes"
        ]
        # Define the Arabic headings that will be displayed in the table
        self.arabic_headings = [
            "اسم الاختبار", "رمز الاختبار", "عدد النماذج المنشورة", "تواريخ النشر", "تواريخ الإيقاف", "نماذج أو أقسام",
            "عدد الاختبارات النشطة", "عدد الاختبارات الغير نشطة", "ملاحظات"
        ]

        # Initialize the Treeview widget with increased row height for better date display
        style = ttk.Style()
        style.configure("Treeview", rowheight=60)  # Increase row height for multi-line dates
        self.tree = ttk.Treeview(parent, columns=self.columns, show="headings", height=15)

        # Configure each column: set heading text (Arabic) and width
        for i, col in enumerate(self.columns):
            self.tree.heading(col, text=self.arabic_headings[i],
                              anchor="e")  # Anchor headings to the right (East) for Arabic
            # Set appropriate column widths for better readability
            col_width = 120
            if col == "test_name":
                col_width = 180
            elif col == "notes":
                col_width = 250
            elif col in ["publish_dates", "stop_dates"]:
                col_width = 150
            elif col == "test_code":
                col_width = 100
            self.tree.column(col, width=col_width, minwidth=80, anchor="e")  # Anchor content to the right (East)

        # Add vertical and horizontal scrollbars to the Treeview
        v_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient="horizontal", command=self.tree.xview)
        # Configure Treeview to use the scrollbars
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack the scrollbars and the Treeview widget
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        self.tree.pack(side="left", fill="both", expand=True)

        # Bind events: double-click to edit, selection change to manage button states
        self.tree.bind("<Double-1>", lambda event: self.open_edit_dialog())  # Double-click to edit
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_selection)  # Update button states on selection change

    def on_tree_selection(self, event=None):
        """Enables/disables Edit and Delete buttons based on whether a row is selected."""
        selected_items = self.tree.selection()
        if selected_items:
            self.edit_btn.configure(state="normal", fg_color="#1f6aa5")
            self.delete_btn.configure(state="normal")
        else:
            self.edit_btn.configure(state="disabled", fg_color="#808080")
            self.delete_btn.configure(state="disabled")

    def refresh_table_display(self, data_to_display: Optional[List[Dict]] = None):
        """Clears the Treeview and repopulates it with the provided or current data."""
        for item in self.tree.get_children():
            self.tree.delete(item)

        data_source = data_to_display if data_to_display is not None else self.tests_data

        for test in data_source:
            values = []
            for col in self.columns:
                if col == "publish_dates":
                    # Combine all publish dates into one column with better formatting
                    dates = []
                    for i in range(1, 4):
                        date = test.get(f"publish_date_{i}", "")
                        if date:
                            dates.append(f"{date}")
                    values.append("\n".join(dates) if dates else "لا توجد تواريخ")
                elif col == "stop_dates":
                    # Combine all stop dates into one column with better formatting
                    dates = []
                    for i in range(1, 4):
                        date = test.get(f"stop_date_{i}", "")
                        if date:
                            dates.append(f"{date}")
                    values.append("\n".join(dates) if dates else "لا توجد تواريخ")
                else:
                    values.append(str(test.get(col, "")))

            self.tree.insert("", "end", values=values)

        self.on_tree_selection()

        if self.tests_data:
            pass  # No charts to manage anymore
        else:
            self.show_charts_btn.configure(text="إظهار الرسوم البيانية")  # Reset button text if no data

    def filter_tests(self, *args):
        """Filters the table data based on the text in the search entry."""
        search_term = self.search_var.get().lower().strip()

        if not search_term:
            self.refresh_table_display()
            return

        filtered_data = []
        for test in self.tests_data:
            test_name = str(test.get("test_name", "")).lower()
            test_code = str(test.get("test_code", "")).lower()
            notes = str(test.get("notes", "")).lower()
            published_models = str(test.get("published_models", "")).lower()
            active_tests = str(test.get("active_tests", "")).lower()
            inactive_tests = str(test.get("inactive_tests", "")).lower()
            models_or_sections = str(test.get("models_or_sections", "")).lower()

            if (search_term in test_name or
                    search_term in test_code or
                    search_term in notes or
                    search_term in published_models or
                    search_term in active_tests or
                    search_term in inactive_tests or
                    search_term in models_or_sections):
                filtered_data.append(test)

        self.refresh_table_display(filtered_data)

    def open_add_dialog(self):
        """Opens the dialog window for adding a new test."""
        dialog = TestDialog(self.root, arabic_font=self.arabic_font, all_tests_data=self.tests_data)
        self.root.wait_window(dialog)

        if dialog.result:
            self.tests_data.append(dialog.result)
            self.refresh_table_display()
            self.save_data_to_file()

    def get_selected_test_data(self) -> Optional[Dict]:
        """Retrieves the data of the currently selected test in the Treeview."""
        selected_item_id = self.tree.selection()
        if not selected_item_id:
            messagebox.showwarning("تحذير", "يرجى تحديد اختبار من الجدول.")
            return None

        item_values = self.tree.item(selected_item_id[0])["values"]

        try:
            test_code_index = self.columns.index("test_code")
            selected_test_code = item_values[test_code_index]
        except (ValueError, IndexError):
            messagebox.showerror("خطأ", "لا يمكن تحديد رمز الاختبار من الجدول.")
            return None

        for test in self.tests_data:
            if test.get("test_code") == selected_test_code:
                return test

        messagebox.showerror("خطأ", f"لم يتم العثور على بيانات الاختبار برمز '{selected_test_code}' في مصدر البيانات.")
        return None

    def open_edit_dialog(self):
        """Opens the dialog window for editing the currently selected test."""
        selected_test = self.get_selected_test_data()
        if not selected_test:
            return

        dialog = TestDialog(self.root, test_data=selected_test.copy(), arabic_font=self.arabic_font,
                            all_tests_data=self.tests_data)
        self.root.wait_window(dialog)

        if dialog.result:
            try:
                test_code_to_update = selected_test.get("test_code")
                if test_code_to_update:
                    for i, test in enumerate(self.tests_data):
                        if test.get("test_code") == test_code_to_update:
                            self.tests_data[i] = dialog.result
                            self.refresh_table_display()
                            self.save_data_to_file()
                            return
                else:
                    messagebox.showerror("خطأ", "لا يمكن تعديل الاختبار بدون رمز اختبار صالح.")
            except Exception as e:
                messagebox.showerror("خطأ في التحديث", f"حدث خطأ أثناء تحديث البيانات: {e}")

    def delete_selected_test(self):
        """Deletes the currently selected test from the data and updates the UI."""
        selected_test = self.get_selected_test_data()
        if not selected_test:
            return

        test_code = selected_test.get("test_code")
        test_name = selected_test.get("test_name", "الاختبار")

        if messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد أنك تريد حذف الاختبار '{test_name}' (رمز: {test_code})؟",
                icon='warning'
        ):
            try:
                self.tests_data = [test for test in self.tests_data if test.get("test_code") != test_code]

                self.refresh_table_display()
                self.save_data_to_file()
                messagebox.showinfo("نجاح", f"تم حذف الاختبار '{test_name}' بنجاح.")
            except Exception as e:
                messagebox.showerror("خطأ في الحذف", f"حدث خطأ أثناء حذف الاختبار: {e}")

    def import_data(self):
        """Handles importing data from an Excel file selected by the user."""
        try:
            filepath = filedialog.askopenfilename(
                title="استيراد البيانات من ملف Excel",
                filetypes=[("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")],
                defaultextension=".xlsx"
            )
            if not filepath: return

            df = pd.read_excel(filepath)
            imported_data = df.to_dict('records')

            arabic_to_english_map = {
                "اسم الاختبار": "test_name", "رمز الاختبار": "test_code", "عدد النماذج المنشورة": "published_models",
                "تاريخ النشر (1)": "publish_date_1", "تاريخ الإيقاف (1)": "stop_date_1",
                "تاريخ النشر (2)": "publish_date_2", "تاريخ الإيقاف (2)": "stop_date_2",
                "تاريخ النشر (3)": "publish_date_3", "تاريخ الإيقاف (3)": "stop_date_3",
                "نماذج أو أقسام": "models_or_sections", "عدد الاختبارات النشطة": "active_tests",
                "عدد الاختبارات الغير نشطة": "inactive_tests", "ملاحظات": "notes"
            }

            processed_data = []
            for item in imported_data:
                converted_item = {}
                for ar_key, en_key in arabic_to_english_map.items():
                    converted_item[en_key] = item.get(ar_key, "")
                processed_data.append(converted_item)

            self.tests_data.extend(processed_data)
            self.refresh_table_display()
            self.save_data_to_file()
            messagebox.showinfo("استيراد ناجح", f"تم استيراد {len(processed_data)} سجل اختبار بنجاح.")

        except FileNotFoundError:
            messagebox.showerror("خطأ", "الملف المحدد غير موجود.")
        except Exception as e:
            messagebox.showerror("خطأ في الاستيراد", f"تعذر استيراد البيانات: {e}")

    def export_data(self):
        """Handles exporting the current data to an Excel file."""
        try:
            filepath = filedialog.asksaveasfilename(
                title="تصدير البيانات إلى ملف Excel",
                filetypes=[("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")],
                defaultextension=".xlsx",
                initialfile="exported_tests_data.xlsx"
            )
            if not filepath: return

            df = pd.DataFrame(self.tests_data)

            english_to_arabic_map = {
                "test_name": "اسم الاختبار", "test_code": "رمز الاختبار", "published_models": "عدد النماذج المنشورة",
                "publish_date_1": "تاريخ النشر (1)", "stop_date_1": "تاريخ الإيقاف (1)",
                "publish_date_2": "تاريخ النشر (2)", "stop_date_2": "تاريخ الإيقاف (2)",
                "publish_date_3": "تاريخ النشر (3)", "stop_date_3": "تاريخ الإيقاف (3)",
                "models_or_sections": "نماذج أو أقسام", "active_tests": "عدد الاختبارات النشطة",
                "inactive_tests": "عدد الاختبارات الغير نشطة", "notes": "ملاحظات"
            }
            df = df.rename(columns=english_to_arabic_map)

            df.to_excel(filepath, index=False)
            messagebox.showinfo("تصدير ناجح", f"تم تصدير البيانات إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ في التصدير", f"تعذر تصدير البيانات: {e}")

    # --- Query Functionality ---
    def open_query_dialog(self):
        """Opens the dialog for performing multi-step queries."""
        query_dialog = QueryDialog(self.root, arabic_font=self.arabic_font, all_tests_data=self.tests_data)
        self.root.wait_window(query_dialog)

        if query_dialog.selected_test_data:
            self.highlight_test_in_table(query_dialog.selected_test_data.get("test_code"))
            # messagebox.showinfo("نتيجة الاستعلام", self.format_test_details(query_dialog.selected_test_data))

    def highlight_test_in_table(self, test_code_to_highlight):
        """Highlights a specific test in the main table by its test code."""
        if not test_code_to_highlight:
            return

        for item_id in self.tree.get_children():
            item_values = self.tree.item(item_id)["values"]
            try:
                if item_values[1] == test_code_to_highlight:
                    self.tree.selection_set(item_id)
                    self.tree.focus(item_id)
                    self.tree.see(item_id)
                    break
            except IndexError:
                continue

    def format_test_details(self, test_data):
        """Helper to format test details for display (e.g., in a message box)."""
        if not test_data:
            return "لا توجد بيانات لعرضها."

        details = "تفاصيل الاختبار:\n\n"
        details += f"  اسم الاختبار: {test_data.get('test_name', 'N/A')}\n"
        details += f"  رمز الاختبار: {test_data.get('test_code', 'N/A')}\n"
        details += f"  النماذج المنشورة: {test_data.get('published_models', 'N/A')}\n"
        details += f"  تاريخ النشر (1): {test_data.get('publish_date_1', 'N/A')}\n"
        details += f"  تاريخ الإيقاف (1): {test_data.get('stop_date_1', 'N/A')}\n"
        details += f"  النماذج/الأقسام: {test_data.get('models_or_sections', 'N/A')}\n"
        details += f"  الاختبارات النشطة: {test_data.get('active_tests', 'N/A')}\n"
        details += f"  الاختبارات غير النشطة: {test_data.get('inactive_tests', 'N/A')}\n"
        details += f"  ملاحظات: {test_data.get('notes', 'N/A')}\n"
        return details

    def run(self):
        """Starts the Tkinter event loop, making the application interactive."""
        self.root.mainloop()


# --- Main Execution Block ---
def main():
    """Main function to initialize and run the application."""
    app = TestManagementApp()
    app.run()


if __name__ == "__main__":
    main()